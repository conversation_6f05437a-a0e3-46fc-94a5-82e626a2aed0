using TMPro;
using UnityEngine;

namespace Modules.UI
{
    public class TextWidget : WidgetBase
    {
        [SerializeField] protected TMP_Text mainText;

        public virtual void SetMessage(string message)
        {
            mainText.text = message;
        }

        public void SetColor(Color color)
        {
            mainText.color = color;
        }

        public void SetFontSize(int fontSize)
        {
            mainText.fontSize = fontSize;
        }
    }
}