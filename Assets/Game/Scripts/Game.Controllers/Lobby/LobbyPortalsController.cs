using System;
using System.Reactive.Linq;
using Cysharp.Threading.Tasks;
using Game.Core;
using Game.Views.Levels;
using Game.Views.Lobby;
using Game.Views.Portals;
using Modules.Core;
using VContainer;

namespace Game.Controllers.Lobby
{
    public class LobbyPortalsController : ControllerBase
    {
        private GameConfig gameConfig;
        private LevelModel levelModel;
        private LobbySpaceManager lobbySpaceManager;

        [Inject]
        private void Construct(LevelModel levelModel, LobbySpaceManager lobbySpaceManager, GameConfig gameConfig)
        {
            this.levelModel = levelModel;
            this.gameConfig = gameConfig;
            this.lobbySpaceManager = lobbySpaceManager;

            levelModel.OnLevelLoaded.Where(ok => ok).Subscribe(_ => HandleLevelLoaded()).AddTo(DisposeCancellationToken);
            lobbySpaceManager.OnPortalTriggered.Subscribe(HandlePortalTriggered).AddTo(DisposeCancellationToken);
            lobbySpaceManager.CustomPortal.OnPlayerTriggered.Subscribe(HandlePortalTriggered).AddTo(DisposeCancellationToken);
        }

        private void HandleLevelLoaded()
        {
            if (!levelModel.IsLobbyLevel)
            {
                return;
            }

            InitializePortals().Forget();
        }

        private void HandlePortalTriggered(PortalView portal)
        {
            if (portal.HasLevelData)
            {
                portal.SetActiveTrigger(false);
                levelModel.StartLevel(new LoadLevelArgs(portal.LevelId, portal.LevelPassword));
            }
        }

        private async UniTaskVoid InitializePortals()
        {
            var lobbyLevelList = await levelModel.GetLobbyLevelList(DisposeCancellationToken);
            lobbySpaceManager.InitializePortals(gameConfig.LobbyPortalList, lobbyLevelList);

            var customPortalLevelData = lobbyLevelList.Find(l => l.id == gameConfig.CustomPortalLevelId);
            if (customPortalLevelData != null)
            {
                lobbySpaceManager.CustomPortal.Initialize(customPortalLevelData);
            }
        }
    }
}