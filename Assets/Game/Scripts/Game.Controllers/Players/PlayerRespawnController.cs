using System;
using Cysharp.Threading.Tasks;
using Game.Views.Checkpoints;
using Game.Views.Levels;
using Game.Views.Lobby;
using Game.Views.Moderation;
using Game.Views.Players;
using Modules.Core;
using UnityEngine;
using VContainer;

namespace Game.Controllers.Players
{
    public class PlayerRespawnController : ControllerBase
    {
        private LevelModel levelModel;
        private LobbySpaceManager lobbySpaceManager;
        private PlayersModel playersModel;
        private LevelSpaceManager levelSpaceManager;
        private ModerationModel moderationModel;
        private CheckpointsManager checkpointsManager;

        private bool IsInPrison => moderationModel.PrisonEndTimer.IsRun;
        private bool IsInRace => checkpointsManager.IsAnyRaceActive;

        [Inject]
        private void Construct(
            PlayersModel playersModel,
            ModerationModel moderationModel,
            LobbySpaceManager lobbySpaceManager,
            LevelModel levelModel,
            LevelSpaceManager levelSpaceManager,
            CheckpointsManager checkpointsManager)
        {
            this.levelModel = levelModel;
            this.lobbySpaceManager = lobbySpaceManager;
            this.levelSpaceManager = levelSpaceManager;
            this.playersModel = playersModel;
            this.moderationModel = moderationModel;
            this.checkpointsManager = checkpointsManager;

            playersModel.OnLocalPlayerRespawning.Subscribe(RespawnPlayer).AddTo(DisposeCancellationToken);
        }

        private void RespawnPlayer(PlayerRespawnArgs args)
        {
            playersModel.TeleportLocalPlayer(new PlayerTeleportArgs(GetSpawnPose(), args.enableLocomotions));
        }

        private Pose GetSpawnPose()
        {
            if (levelModel.IsLobbyLevel)
            {
                if (IsInPrison)
                {
                    return lobbySpaceManager.GetPrisonSpawnPose();
                }

                return IsInRace ? checkpointsManager.RaceCancelSpawnPoint.GetPose() : lobbySpaceManager.GetHomeSpawnPose();
            }

            return IsInRace ? checkpointsManager.RaceCancelSpawnPoint.GetPose() : levelSpaceManager.GetPortalSpawnPose();
        }
    }
}