using System;
using System.Reactive;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Game.Core;
using Game.Views.Levels;
using Game.Views.UI.Screens.PrivateLobby;
using Modules.Core;
using Modules.Network;
using Modules.UI;
using VContainer;

namespace Game.Controllers.Screens
{
    public class PrivateLobbyScreenController : ControllerBase
    {
        private PrivateLobbyScreen privateLobbyScreen;
        private GameConfig gameConfig;
        private LevelModel levelModel;

        [Inject]
        private void Construct(IScreenManager screenManager, INetworkClient networkClient, GameConfig gameConfig, LevelModel levelModel)
        {
            this.gameConfig = gameConfig;
            this.levelModel = levelModel;

            privateLobbyScreen = screenManager.GetScreen<PrivateLobbyScreen>();
            privateLobbyScreen.SetActiveLoadingState(true);
            privateLobbyScreen.OnPrivateLobbySubmitted.Subscribe(HandlePrivateLobbyIdSubmitted).AddTo(DisposeCancellationToken);
            privateLobbyScreen.OnConnectToPublicLobbyPressed.Subscribe(HandleBackToPublicLobbyPressed).AddTo(DisposeCancellationToken);
            networkClient.IsConnected.Subscribe(HandleNetworkConnectedState).AddTo(DisposeCancellationToken);
        }

        private void HandleNetworkConnectedState(bool connected)
        {
            privateLobbyScreen.SetActiveLoadingState(!connected);
        }

        private void HandlePrivateLobbyIdSubmitted(string privateLobbyId)
        {
            if (string.IsNullOrEmpty(privateLobbyId))
            {
                privateLobbyScreen.RenderInfo("Please enter a valid lobby name", 2);
                return;
            }

            gameConfig.SetPrivateLobbyName(privateLobbyId);
            levelModel.StartLobbyLevel();
        }

        private void HandleBackToPublicLobbyPressed(Unit unit)
        {
            gameConfig.SetPrivateLobbyName(null);
            levelModel.StartLobbyLevel();
        }
    }
}