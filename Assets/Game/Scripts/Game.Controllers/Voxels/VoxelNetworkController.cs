using System;
using Cysharp.Threading.Tasks;
using Game.Views.Voxels;
using Modules.Core;
using Modules.Network;
using Modules.XR;
using UnityEngine;
using VContainer;

namespace Game.Controllers.Voxels
{
    public class VoxelNetworkController : ControllerBase
    {
        private IXRPlayer xrPlayer;
        private VoxelSpaceManager voxelSpaceManager;
        private VoxelConfig voxelConfig;
        private INetworkClient networkClient;

        [Inject]
        private void Construct(VoxelSpaceManager voxelSpaceManager, VoxelConfig voxelConfig, IXRPlayer xrPlayer, INetworkClient networkClient)
        {
            this.xrPlayer = xrPlayer;
            this.voxelConfig = voxelConfig;
            this.voxelSpaceManager = voxelSpaceManager;
            this.networkClient = networkClient;

            voxelSpaceManager.OnInitialVoxelCreating.Subscribe(HandleInitialVoxelCreating).AddTo(DisposeCancellationToken);
            voxelSpaceManager.OnInitialVoxelDestroying.Subscribe(HandleInitialVoxelDestroying).AddTo(DisposeCancellationToken);
            voxelSpaceManager.OnVoxelCreating.Subscribe(HandleVoxelCreating).AddTo(DisposeCancellationToken);
            voxelSpaceManager.OnVoxelDestroying.Subscribe(HandleVoxelDestroying).AddTo(DisposeCancellationToken);
        }

        private void HandleInitialVoxelCreating(CreatingVoxelArgs args)
        {
            voxelSpaceManager.CreateVoxel(args.position, args.id, args.rotation, false);
        }

        private void HandleInitialVoxelDestroying(DestroyingVoxelArgs args)
        {
            voxelSpaceManager.DamageVoxel(args.position, byte.MaxValue - 1, false);
        }

        private void HandleVoxelCreating(CreatingVoxelArgs args)
        {
            var isPlaySound = !args.ignoreSound && IsPlaySound(args.position);
            voxelSpaceManager.CreateVoxel(args.position, args.id, isPlaySound);
        }

        private void HandleVoxelDestroying(DestroyingVoxelArgs args)
        {
            voxelSpaceManager.DestroyVoxel(args.position, IsPlaySound(args.position), args.byLocalPlayer);
        }

        private bool IsPlaySound(Vector3 position)
        {
            return (xrPlayer.HeadNode.position - position).sqrMagnitude < voxelConfig.PlayingSoundSqrDistance;
        }
    }
}