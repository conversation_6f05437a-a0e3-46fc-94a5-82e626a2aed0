using System;
using System.Reactive.Subjects;
using Cysharp.Threading.Tasks;
using Fusion;
using Modules.Network;
using UnityEngine;

namespace Game.Views.Voxels
{
    public class VoxelsMasterClientObject : NetworkActor
    {
        private readonly IAsyncReactiveProperty<int> sliceServerTime = new AsyncReactiveProperty<int>(0);
        private readonly ISubject<CreatingVoxelArgs> onVoxelCreating = new Subject<CreatingVoxelArgs>();
        private readonly ISubject<DestroyingVoxelArgs> onVoxelDestroying = new Subject<DestroyingVoxelArgs>();
        private readonly ISubject<CreatingVoxelArgs> onInitialVoxelCreating = new Subject<CreatingVoxelArgs>();
        private readonly ISubject<DestroyingVoxelArgs> onInitialVoxelDestroying = new Subject<DestroyingVoxelArgs>();
        private readonly ISubject<PlayerRef> onInitialVoxelsSending = new Subject<PlayerRef>();
        private readonly ISubject<PlayerRef> onInitialVoxelsCompleting = new Subject<PlayerRef>();

        [Networked] [OnChangedRender(nameof(ChangeSliceServerTimeNetwork))]
        private ushort SliceServerTimeNetwork { get; set; }

        public IReadOnlyAsyncReactiveProperty<int> SliceServerTime => sliceServerTime;
        public IObservable<CreatingVoxelArgs> OnVoxelCreating => onVoxelCreating;
        public IObservable<DestroyingVoxelArgs> OnVoxelDestroying => onVoxelDestroying;
        public IObservable<CreatingVoxelArgs> OnInitialVoxelCreating => onInitialVoxelCreating;
        public IObservable<DestroyingVoxelArgs> OnInitialVoxelDestroying => onInitialVoxelDestroying;
        public IObservable<PlayerRef> OnInitialVoxelsSending => onInitialVoxelsSending;
        public IObservable<PlayerRef> OnInitialVoxelsCompleting => onInitialVoxelsCompleting;

        public override void Spawned()
        {
            base.Spawned();
            ChangeSliceServerTimeNetwork();
        }

        public void CreateVoxel(Vector3 point, int id, int time, bool ignoreSound)
        {
            SendRpcSafe(() =>
            {
                if (ignoreSound)
                {
                    CreateVoxelWithoutSoundRpc(new VoxelPoint(point), (byte)id, (ushort)time);
                }
                else
                {
                    CreateVoxelRpc(new VoxelPoint(point), (byte)id, (ushort)time);
                }
            });
        }

        public void CreateVoxel(Vector3 point, int id, byte rotation, int time, bool ignoreSound)
        {
            SendRpcSafe(() =>
            {
                if (ignoreSound)
                {
                    CreateVoxelWithRotationWithoutSoundRpc(new VoxelPoint(point), (byte)id, rotation, (ushort)time);
                }
                else
                {
                    CreateVoxelWithRotationRpc(new VoxelPoint(point), (byte)id, rotation, (ushort)time);
                }
            });
        }

        public void DestroyVoxel(Vector3 point, int time)
        {
            SendRpcSafe(() => DestroyVoxelRpc(new VoxelPoint(point), (ushort)time));
        }

        public void CreateInitialVoxel(PlayerRef player, Vector3 point, int index, int time)
        {
            SendRpcSafe(() => CreateInitialVoxelRpc(player, new VoxelPoint(point), (byte)index, (ushort)time));
        }

        public void DestroyInitialVoxel(PlayerRef player, Vector3 point, int time)
        {
            SendRpcSafe(() => DestroyInitialVoxelRpc(player, new VoxelPoint(point), (ushort)time));
        }

        public void ReceiveInitialVoxels(PlayerRef player)
        {
            SendRpcSafe(() => ReceiveInitialVoxelsRpc(player));
        }

        public void CompleteInitialVoxels(PlayerRef player)
        {
            SendRpcSafe(() => CompleteInitialVoxelsRpc(player));
        }

        public void SetSliceServerTimeTick(int sliceServerTimeValue)
        {
            SliceServerTimeNetwork = (ushort)sliceServerTimeValue;
        }

        [Rpc(RpcSources.All, RpcTargets.All)]
        private void CreateVoxelRpc(VoxelPoint point, byte id, ushort time, RpcInfo info = default)
        {
            var byLocalPlayer = info.Source == Runner.LocalPlayer;
            onVoxelCreating.OnNext(new CreatingVoxelArgs(point.AsVector3(), id, time, false, byLocalPlayer));
        }

        [Rpc(RpcSources.All, RpcTargets.All)]
        private void CreateVoxelWithoutSoundRpc(VoxelPoint point, byte id, ushort time, RpcInfo info = default)
        {
            var byLocalPlayer = info.Source == Runner.LocalPlayer;
            onVoxelCreating.OnNext(new CreatingVoxelArgs(point.AsVector3(), id, time, true, byLocalPlayer));
        }

        [Rpc(RpcSources.All, RpcTargets.All)]
        private void CreateVoxelWithRotationRpc(VoxelPoint point, byte id, byte rotation, ushort time, RpcInfo info = default)
        {
            var byLocalPlayer = info.Source == Runner.LocalPlayer;
            onVoxelCreating.OnNext(new CreatingVoxelArgs(point.AsVector3(), id, time, false, byLocalPlayer, rotation));
        }

        [Rpc(RpcSources.All, RpcTargets.All)]
        private void CreateVoxelWithRotationWithoutSoundRpc(VoxelPoint point, byte id, byte rotation, ushort time, RpcInfo info = default)
        {
            var byLocalPlayer = info.Source == Runner.LocalPlayer;
            onVoxelCreating.OnNext(new CreatingVoxelArgs(point.AsVector3(), id, time, true, byLocalPlayer, rotation));
        }

        [Rpc(RpcSources.All, RpcTargets.All)]
        private void DestroyVoxelRpc(VoxelPoint point, ushort time, RpcInfo info = default)
        {
            var byLocalPlayer = info.Source == Runner.LocalPlayer;
            onVoxelDestroying.OnNext(new DestroyingVoxelArgs(point.AsVector3(), time, byLocalPlayer));
        }

        [Rpc(RpcSources.All, RpcTargets.All)]
        private void CreateInitialVoxelRpc([RpcTarget] PlayerRef player, VoxelPoint point, byte id, ushort time, RpcInfo info = default)
        {
            var byLocalPlayer = info.Source == Runner.LocalPlayer;
            onInitialVoxelCreating.OnNext(new CreatingVoxelArgs(point.AsVector3(), id, time, true, byLocalPlayer));
        }

        [Rpc(RpcSources.All, RpcTargets.All)]
        private void DestroyInitialVoxelRpc([RpcTarget] PlayerRef player, VoxelPoint point, ushort time, RpcInfo info = default)
        {
            var byLocalPlayer = info.Source == Runner.LocalPlayer;
            onInitialVoxelDestroying.OnNext(new DestroyingVoxelArgs(point.AsVector3(), time, byLocalPlayer));
        }

        [Rpc(RpcSources.All, RpcTargets.All)]
        private void ReceiveInitialVoxelsRpc([RpcTarget] PlayerRef player, RpcInfo rpcInfo = default)
        {
            onInitialVoxelsSending.OnNext(rpcInfo.Source);
        }

        [Rpc(RpcSources.All, RpcTargets.All)]
        private void CompleteInitialVoxelsRpc([RpcTarget] PlayerRef player)
        {
            onInitialVoxelsCompleting.OnNext(player);
        }

        private void ChangeSliceServerTimeNetwork()
        {
            if (sliceServerTime.Value != SliceServerTimeNetwork)
            {
                sliceServerTime.Value = SliceServerTimeNetwork;
            }
        }
    }
}