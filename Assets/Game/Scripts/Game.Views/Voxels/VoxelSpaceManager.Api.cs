using System;
using System.Collections.Generic;
using Game.Core;
using Game.Core.Exceptions;
using Modules.Core;
using UnityEngine;
using VoxelPlay;

namespace Game.Views.Voxels
{
    public partial class VoxelSpaceManager
    {
        private readonly Collider[] bufferColliders = new Collider[1];

        public byte[] GetMap()
        {
            if (!IsInitialized)
            {
                return null;
            }

            return voxelEnvironment.SaveGameToByteArray();
        }

        private void SetMap(byte[] map)
        {
            try
            {
                voxelEnvironment.LoadGameFromByteArray(map, false);
                SetMapGenerationEnabled(true);
            }
            catch (Exception e)
            {
                GameLogger.Voxels.Fatal(new WorldGenerationException(e.Message));
            }
        }

        public void CreateModel(ModelDefinition model, Vector3 position, int rotation)
        {
            if (!IsInitialized)
            {
                return;
            }

            voxelEnvironment.ModelPlace(position, model, rotation);
        }

        public void SetSpreadingEnabled(bool spreadingEnabled)
        {
            this.spreadingEnabled = spreadingEnabled;
        }

        public void SetMapGenerationEnabled(bool mapGenerationEnabled)
        {
            voxelEnvironment.enableGeneration = mapGenerationEnabled;
        }

        public void SetTimeOfDay(float time, float azimuth = 180)
        {
            if (!IsInitialized)
            {
                return;
            }

            voxelEnvironment.SetTimeOfDay(time, azimuth);
        }

        public bool RaycastVoxel(Vector3 point, Vector3 direction, out VoxelHitInfo hit)
        {
            return RaycastVoxel(point, direction, voxelConfig.RaycastDistance, out hit);
        }

        public bool RaycastVoxel(Vector3 point, Vector3 direction, float maxDistance, out VoxelHitInfo hit)
        {
            if (!IsInitialized)
            {
                hit = default;
                return false;
            }

            return voxelEnvironment.RayCast(point, direction, out hit, maxDistance, colliderTypes: ColliderTypes.OnlyVoxels, layerMask: 1 << voxelEnvironment.layerVoxels);
        }

        public bool OverlapVoxel(Vector3 point)
        {
            return Physics.OverlapBoxNonAlloc(point, 0.51f * voxelConfig.GridSize * Vector3.one, bufferColliders, Quaternion.identity, 1 << voxelEnvironment.layerVoxels) > 0;
        }

        public bool OverlapPlayer(Vector3 point)
        {
            const int mask = (1 << Layers.XRPlayer) | (1 << Layers.RemotePlayer) | (1 << Layers.Enemy);
            var extents = 0.5f * Vector3.one * voxelConfig.GridSize;
            return Physics.OverlapBoxNonAlloc(point, extents, bufferColliders, Quaternion.identity, mask, QueryTriggerInteraction.Collide) > 0;
        }

        int rot;
        public void CreateVoxel(Vector3 point, int id, bool isPlaySound = true)
        {
            if (!IsInitialized || CheckCollision(point) || !voxelConfig.TryGetVoxelDef(id, out var voxel))
            {
                return;
            }

            voxelEnvironment.VoxelPlace(point, voxel, tintColor: voxel.tintColor, playSound: isPlaySound, rotation: rot);
            // rot = (rot + 1) % 4;
        }

        public void CreateVoxel(Vector3 point, int id, byte rotation, bool isPlaySound = true)
        {
            if (!IsInitialized || CheckCollision(point) || !voxelConfig.TryGetVoxelDef(id, out var voxel))
            {
                return;
            }

            voxelEnvironment.VoxelPlace(point, voxel, tintColor: voxel.tintColor, playSound: isPlaySound, rotation: rotation);
        }

        public bool DamageVoxel(Vector3 point, int damage, bool isPlaySound = true)
        {
            return IsInitialized && voxelEnvironment.VoxelDamage(point, damage, isPlaySound);
        }

        public bool DestroyVoxel(Vector3 point, bool isPlaySound, bool byLocalPlayer)
        {
            if (!IsInitialized)
            {
                return false;
            }

            if (TryGetVoxelDefinition(point, out var voxelDefinition))
            {
                onVoxelDestroyed.OnNext(new DestroyedVoxelArgs(point, voxelDefinition, byLocalPlayer));
            }

            return voxelEnvironment.VoxelDamage(point, byte.MaxValue - 1, isPlaySound);
        }

        public bool DamageVoxel(Vector3 point, int damage, int radius, List<VoxelIndex> result = null)
        {
            return IsInitialized && voxelEnvironment.VoxelDamage(point, damage, radius, false, false, result) > 0;
        }

        public bool DestroyVoxel(Vector3 point)
        {
            return IsInitialized && voxelEnvironment.VoxelDestroy(point);
        }

        public bool CheckCollision(Vector3 point)
        {
            return IsInitialized && voxelEnvironment.CheckCollision(point);
        }

        public VoxelPlaceholder GetVoxelPlaceholder(VoxelChunk chunk, int voxelIndex)
        {
            return IsInitialized ? voxelEnvironment.GetVoxelPlaceholder(chunk, voxelIndex) : null;
        }

        public Vector3 GetVoxelCenter(Vector3 point)
        {
            return new Vector3(GetSnapValue(point.x), GetSnapValue(point.y), GetSnapValue(point.z));
        }

        public float GetTerrainHeight(Vector3 point)
        {
            if (!IsInitialized)
            {
                return 0;
            }

            if (RaycastVoxel(point.SetY(voxelConfig.WorldHeight), Vector3.down, voxelConfig.WorldHeight, out var hit))
            {
                return (float)hit.point.y;
            }

            return voxelEnvironment.GetTerrainHeight(point);
        }

        public bool TryGetPointOnVoxel(ref Vector3 point, ref Vector3 normal)
        {
            var origin = point + 0.1f * normal;
            var direction = -normal;

            if (RaycastVoxel(origin, direction, out var hit))
            {
                normal = hit.normal;
                point = hit.voxelCenter + 0.5f * normal * voxelConfig.GridSize;
                return true;
            }

            return false;
        }

        public Vector3 GetRandomPointOnTerrain()
        {
            return GetRandomPointOnTerrain(Vector3.zero, (int)voxelEnvironment.world.extents.x);
        }

        public Vector3 GetRandomPointOnTerrain(Vector3 origin, int radius)
        {
            var point = origin + new Vector3(Randomizer.Range(-radius, radius), 0, Randomizer.Range(-radius, radius));
            var center = GetVoxelCenter(point);
            center.y = GetTerrainHeight(center);
            return center;
        }

        public bool TryGetRandomPointOnTerrain(Vector3 origin, Vector3 bounds, out Vector3 point)
        {
            var randomPoint = origin + new Vector3(Randomizer.Range(-bounds.x, bounds.x), bounds.y, Randomizer.Range(-bounds.z, bounds.z));
            if (Physics.Raycast(randomPoint, Vector3.down, out var hit, bounds.y * 2, 1 << Layers.Default))
            {
                point = hit.point + new Vector3(0, 1, 0);
                return true;
            }

            point = Vector3.zero;
            return false;
        }

        public bool IsWaterVoxel(Vector3 point)
        {
            if (!IsInitialized)
            {
                return false;
            }

            if (voxelEnvironment.GetVoxelIndex(point + 0.1f * Vector3.down, out var chunk, out var voxelIndex, false))
            {
                var voxel = chunk.voxels[voxelIndex];
                return voxel.hasWater;
            }

            return false;
        }

        public bool TryGetVoxelDefinition(Vector3 point, out VoxelDefinition voxelDefinition)
        {
            if (!IsInitialized)
            {
                voxelDefinition = null;
                return false;
            }

            if (!voxelEnvironment.GetVoxelIndex(point, out var chunk, out var voxelIndex, false))
            {
                voxelDefinition = null;
                return false;
            }

            voxelDefinition = voxelEnvironment.GetVoxelDefinition(chunk.voxels[voxelIndex].typeIndex);
            return voxelDefinition != null;
        }

        private bool TryGetVoxelId(Vector3 point, out int id, out byte rotation)
        {
            if (!IsInitialized)
            {
                id = 0;
                rotation = 0;
                return false;
            }

            if (!voxelEnvironment.GetVoxelIndex(point, out var chunk, out var voxelIndex, false))
            {
                id = 0;
                rotation = 0;
                return false;
            }

            var voxel = chunk.voxels[voxelIndex];
            if (voxel.typeIndex == 0)
            {
                id = 0;
                rotation = 0;
                return false;
            }

            var voxelDefinition = voxelEnvironment.GetVoxelDefinition(voxel.typeIndex);
            if (voxelDefinition == null)
            {
                id = 0;
                rotation = 0;
                return false;
            }
            
            rotation = (byte) voxel.GetTextureRotation();
            return voxelConfig.TryGetVoxelDefId(voxelDefinition, out id);
        }

        private float GetSnapValue(float value)
        {
            return Mathf.FloorToInt(value / voxelConfig.GridSize) * voxelConfig.GridSize + 0.5f * voxelConfig.GridSize;
        }
    }
}